//
//  EventDetailViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-29.
//  Copyright © 2024 AICoin. All rights reserved.
//

import SnapKit
import UIKit
import WebKit

class EventDetailViewController: AICBaseViewController {

    // MARK: - 属性

    private let event: CalendarEventModel
    var textFont = "10px"

    // 滚动视图
    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = .clear
        return scrollView
    }()

    // 内容容器
    private let contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .designKit.secondaryBackgroundNew
        return view
    }()

    let webView: MomentsArticleDetailView = {
        let webView = MomentsArticleDetailView()
        webView.backgroundColor = .clear
        return webView
    }()

    private let topDivider: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.bgColor
        return view
    }()

    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = .themeColor(day: 0x292D33, night: 0xffffff)
        label.numberOfLines = 0
        return label
    }()

    // 标签滚动视图
    private let tagsScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()

    // 标签容器
    private let tagsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .center
        stackView.distribution = .fill
        stackView.spacing = 8
        return stackView
    }()

    // 底部操作区
    private let bottomActionsView: UIView = {
        let view = UIView()
        return view
    }()
    // 提醒按钮
    private let reminderButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_bell"), for: .normal)
        button.contentMode = .scaleAspectFit
        button.tintColor = UIColor(hexString: "#B7BFC8")
        return button
    }()

    // 分享按钮
    private let shareButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_share"), for: .normal)
        button.contentMode = .scaleAspectFit
        button.tintColor = UIColor(hexString: "#B7BFC8")
        return button
    }()

    // MARK: - 初始化

    init(event: CalendarEventModel) {
        self.event = event
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "事件详情".base.localized

        setupViews()
        configureWithEvent()
    }

    // MARK: - 设置视图

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        // 添加滚动视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        // 添加内容视图
        contentView.addSubview(topDivider)
        contentView.addSubview(titleLabel)

        // 添加WebView到内容视图
        contentView.addSubview(webView)

        contentView.addSubview(tagsScrollView)

        tagsScrollView.addSubview(tagsStackView)

        contentView.addSubview(bottomActionsView)
        bottomActionsView.addSubview(reminderButton)
        bottomActionsView.addSubview(shareButton)

        // 设置约束
        scrollView.snp.makeConstraints { make in
            make.top.left.right.bottom.equalTo(view)
            make.bottom.equalTo(contentView.snp.bottom)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.left.right.equalTo(view)
            make.width.equalTo(scrollView)
            // 高度会动态调整
        }

        topDivider.snp.makeConstraints { make in
            make.top.equalTo(contentView)
            make.left.right.equalTo(contentView)
            make.height.equalTo(4)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(topDivider.snp.bottom).offset(8)
            make.left.right.equalTo(contentView).inset(16)
        }

        // 设置WebView约束
        webView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalTo(contentView)
            make.height.equalTo(300)  // 初始高度，会根据内容动态调整
            make.bottom.equalTo(contentView).offset(-8)
            make.width.equalTo(contentView)
        }

        tagsScrollView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.top.equalTo(webView.snp.bottom).offset(8)
            
            make.right.equalTo(contentView).offset(-16)
        }

        tagsStackView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(40)
        }

        bottomActionsView.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(tagsScrollView.isHidden ? webView.snp.bottom : tagsScrollView.snp.bottom).offset(8)
            make.height.equalTo(40)
        }

        reminderButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        shareButton.snp.makeConstraints { make in
            make.right.equalTo(reminderButton.snp.left).offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }


        // // 添加按钮事件
        reminderButton.addTarget(self, action: #selector(reminderButtonTapped), for: .touchUpInside)
        shareButton.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
    }

    // MARK: - 配置视图数据

    private func configureWithEvent() {
        // 设置标题
        titleLabel.text = event.title

        // 设置WebView内容
        // loadHTML(string: event.content ?? "")
        loadHTML(string: "<p>2025年05月06日08:00（UTC+8），币安Launchpool现已上线第69期项目&nbsp;-&nbsp;&nbsp;Space&nbsp;and&nbsp;Time&nbsp;(SXT)&nbsp;&nbsp;，一个由微软支持、以ZK技术验证过的数据为基础的区块链。</p>\n\n<p>币安将BNB、FDUSD和USDC投入到SXT奖励池中获得SXT，活动时长共计2天。</p>\n\n<p>奖励池：</p>\n\n<p>BNB池子（网站将于此公告十二小时内，活动开放前更新）：总奖励&nbsp;106,250,000&nbsp;SXT（占85%）</p>\n\n<p>FDUSD池子（网站将于此公告十二小时内，活动开放前更新）：总奖励&nbsp;6,250,000&nbsp;SXT（占5%）</p>\n\n<p>USDC池子（网站将于此公告十二小时内，活动开放前更新）：总奖励&nbsp;12,500,000&nbsp;SXT（占10%）</p>\n\n<p>活动时间：2025年05月06日08:00（东八区时间）至2025年05月08日07:59（东八区时间）</p><p>2025年05月06日08:00（UTC+8），币安Launchpool现已上线第69期项目&nbsp;-&nbsp;&nbsp;Space&nbsp;and&nbsp;Time&nbsp;(SXT)&nbsp;&nbsp;，一个由微软支持、以ZK技术验证过的数据为基础的区块链。</p>\n\n<p>币安将BNB、FDUSD和USDC投入到SXT奖励池中获得SXT，活动时长共计2天。</p>\n\n<p>奖励池：</p>\n\n<p>BNB池子（网站将于此公告十二小时内，活动开放前更新）：总奖励&nbsp;106,250,000&nbsp;SXT（占85%）</p>\n\n<p>FDUSD池子（网站将于此公告十二小时内，活动开放前更新）：总奖励&nbsp;6,250,000&nbsp;SXT（占5%）</p>\n\n<p>USDC池子（网站将于此公告十二小时内，活动开放前更新）：总奖励&nbsp;12,500,000&nbsp;SXT（占10%）</p>\n\n<p>活动时间：2025年05月06日08:00（东八区时间）至2025年05月08日07:59（东八区时间）</p>")
        // 创建并添加标签
        setupTags()
    }

    func loadHTML(string: String) {
        let color = isNightTheme ? "#6C7884" : "#323232"
        let hstr =
            "<html><header><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></header><body style='font-size:\(textFont);line-height:27px;font-family:Arial,PingFang SC;color:\(color);padding:0;margin:0'>\(string)</body></html>"
        webView.loadHTML(string: hstr)
        webView.finishBlock = { [weak self] view in
            guard let self = self else { return }
            //  之前设置的 cellHeight 不包含 webView 的高度，并不准确，因此这里加上以后再次赋值
            self.webView.height = view.height
            self.webView.snp.updateConstraints { make in
                make.height.equalTo(view.height)
                make.left.right.equalToSuperview()
                make.top.equalTo(self.titleLabel.snp.bottom).offset(8)
                make.bottom.equalTo(self.contentView).offset(-8)
            }
            // 触发布局刷新
            self.view.layoutIfNeeded()
        }
    }

    private func setupTags() {
        // 清除现有标签
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 保存relatedEntrances以便点击事件使用
        let entrances = event.relatedEntrances

        // 创建并添加标签
        for (index, entrance) in entrances.enumerated() {
            let tagView = createTagLabel(with: entrance)
            tagView.tag = index
            tagsStackView.addArrangedSubview(tagView)
        }

        // 如果没有标签，隐藏标签滚动视图
        tagsScrollView.isHidden = entrances.isEmpty
    }

    // 创建标签方法
    private func createTagLabel(with entrance: CalendarRelatedEntrance) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor(hexString: "#F9FAFC")
        containerView.layer.cornerRadius = 4
        containerView.clipsToBounds = true

        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hexString: "#398EFF")
        label.text = entrance.displayText

        containerView.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(8)
            make.top.bottom.equalToSuperview().inset(4)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(tagTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true

        return containerView
    }

    // MARK: - 格式化日期

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日HH:mm（'UTC'Z）"
        return formatter.string(from: date)
    }

    // MARK: - 按钮事件

    @objc private func reminderButtonTapped() {
        // 打开事件提醒设置
        let reminderVC = EventReminderViewController(event: event)
        present(reminderVC, animated: true, completion: nil)
    }

    @objc private func shareButtonTapped() {
        // 分享功能暂不实现
        // TODO: 实现分享功能
    }

    @objc private func tagTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        if index < event.relatedEntrances.count {
            let entrance = event.relatedEntrances[index]

            if entrance.entranceType == 1 {
                // K线入口，跳转K线详情页
                UIViewController.aic_getRootNavigationController()?.jumpToTickerDetailVC(
                    firstKey: entrance.key, listKeys: nil)
            } else {
                // 网页入口，跳转H5页面
                UIViewController.aic_getRootNavigationController()?.jumpToWebViewController(
                    entrance.link)
            }
        }
    }
}
