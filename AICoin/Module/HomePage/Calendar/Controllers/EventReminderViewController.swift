//
//  EventReminderViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-22.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit

/// 事件提醒设置视图控制器
class EventReminderViewController: AICBaseCustomPresentationViewController {

    // MARK: - 属性

    var event: CalendarEventModel
    var reminderView: EventReminderView?

    // MARK: - 初始化
    
    override func presentationController(forPresented presented: UIViewController, presenting: UIViewController?, source: UIViewController) -> UIPresentationController? {
        return AICPresentationKeyboardController(presentedViewController: presented, presenting: presented, style: presentationControllerStyle)
    }
    init(event: CalendarEventModel) {
        self.event = event
        super.init(style: .actionSheet)  // 使用 actionSheet 样式从底部弹出
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // MARK: - UI设置

    private func setupUI() {
        // 设置内容大小 - 根据内容动态计算高度
        self.preferredContentSize = CGSize(width: UIScreen.main.bounds.width, height: 600)

        // 创建并配置提醒设置视图
        let reminderView = EventReminderView(frame: view.bounds)
        reminderView.configure(with: event)
        view.addSubview(reminderView)
        self.reminderView = reminderView

        // 设置提醒视图约束
        reminderView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    // MARK: - 呈现方法

    /// 在指定视图控制器上呈现事件提醒设置弹窗
    /// - Parameters:
    ///   - event: 要设置提醒的事件
    ///   - viewController: 呈现弹窗的视图控制器
    static func present(for event: CalendarEventModel, from viewController: UIViewController) {
        let reminderVC = EventReminderViewController(event: event)
        viewController.present(reminderVC, animated: true, completion: nil)
    }
}
