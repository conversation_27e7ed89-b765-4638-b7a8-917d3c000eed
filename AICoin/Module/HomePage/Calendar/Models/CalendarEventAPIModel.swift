//
//  CalendarEventAPIModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

/// 日历事件API响应模型
@objcMembers
class CalendarEventAPIResponse: NSObject {
    var data: CalendarEventAPIData?

    class func modelCustomPropertyMapper() -> [String: String] {
        return [:]
    }
}

/// 日历事件API数据模型
@objcMembers
class CalendarEventAPIData: NSObject {
    var list: [CalendarEventAPIItem] = []

    class func modelContainerPropertyGenericClass() -> [String: Any] {
        return ["list": CalendarEventAPIItem.self]
    }
}

/// 日历事件相关入口API模型
@objcMembers
class CalendarEventAPIRelatedEntrance: NSObject {
    var entranceType: Int = 0
    var name: String = ""
    var link: String = ""
    var coinShow: String = ""
    var marketName: String = ""
    var key: String = ""
}

/// 日历事件API项目模型
@objcMembers
class CalendarEventAPIItem: NSObject {
    var content: String = ""
    var relatedEntrances: [CalendarEventAPIRelatedEntrance] = []
    var id: String = ""
    var isPending: String = "0"
    var isRemind: String = "0"
    var score: String = "0"
    var startDate: NSNumber = 0
    var title: String = ""
    var type: String = ""

    class func modelContainerPropertyGenericClass() -> [String: Any] {
        return ["relatedEntrances": CalendarEventAPIRelatedEntrance.self]
    }
}
